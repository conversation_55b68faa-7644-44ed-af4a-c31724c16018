import numpy as np

def relative_boundary_distance(points, centers, axes, angles):
    """
    Compute the relative distance to the ellipse boundary (loop over ellipses, vectorized over points)
    
    Parameters:
    - points: array-like, shape (n_points, 2)
    - centers: array-like, shape (n_ellipses, 2)
    - axes: array-like, shape (n_ellipses, 2), semi-axes [a, b] per ellipse
    - angles: array-like, shape (n_ellipses,), rotation angles in radians    
    
    Returns:
    - float array, shape (n_ellipses,): minimum distance to ellipse boundary for each ellipse
    """
    points = np.asarray(points)
    centers = np.asarray(centers)
    axes = np.asarray(axes)
    angles = np.asarray(angles)
        
    n_ellipses = centers.shape[0]
    results = np.zeros(n_ellipses, dtype=np.float32)
    
    for i in range(n_ellipses):
        center = centers[i]
        ax = axes[i]
        theta = angles[i]
        
        translated = points - center        
       
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        rot_matrix = np.array([[cos_theta, sin_theta],
                                [-sin_theta, cos_theta]])
        translated = translated @ rot_matrix.T
        
        val = np.min((translated[:, 0] ** 2 / ax[0] ** 2) + (translated[:, 1] ** 2 / ax[1] ** 2))        
        if val < 1:
            print(f'inside: {val}')
        else:
            print(f'outside: {val-1}')

        results[i] = val
    
    return results

# Example usage with random data
if __name__ == "__main__":
    np.random.seed(42)
    points = np.random.uniform(-500, 100, (2000, 2))
    centers = np.random.uniform(-5, 5, (30, 2))
    axes = np.random.uniform(1, 5, (30, 2))    
    angles = np.random.uniform(0, 2 * np.pi, 30)

    #one ellipse   
    # points = np.array([[5.65886,  6.33957]]) #outside
    # points = np.array([[5.32309,  6.05546]]) #inside    
    # centers = np.array([[0,  0]])
    # axes = np.array([[20, 15]]) /2    
    # angles = np.array([0.0])
    
    result_looped = relative_boundary_distance(points, centers, axes, angles)